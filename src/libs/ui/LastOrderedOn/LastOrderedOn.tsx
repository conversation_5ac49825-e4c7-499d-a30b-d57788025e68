import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
import { Icon } from '@/libs/icons/Icon';
import dayjs from 'dayjs';

export const LastOrderedOn = ({
  lastOrderedQuantity,
  lastOrderedAt,
}: {
  lastOrderedQuantity: number;
  lastOrderedAt: string;
}) => {
  return (
    <div className="mt-3 flex items-center">
      <Icon name="clock" />
      <span className="ml-4 text-xs">
        {lastOrderedQuantity} on{' '}
        {dayjs(lastOrderedAt).format(DEFAULT_DISPLAY_DATE_FORMAT)}
      </span>
    </div>
  );
};

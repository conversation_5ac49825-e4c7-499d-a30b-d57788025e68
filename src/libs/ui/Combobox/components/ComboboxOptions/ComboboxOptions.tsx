import { type ReactNode, useEffect, Children, isValidElement } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { useComboboxContext } from '../../ComboboxContext';
import { mergeClasses } from '@/utils';

const comboboxOptionsVariants = cva(
  'absolute z-10 mt-1 overflow-auto bg-white w-full',
  {
    variants: {
      variant: {
        default: 'border rounded-lg border-gray-300',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

interface ComboboxOptionsProps
  extends VariantProps<typeof comboboxOptionsVariants> {
  children: ReactNode;
  className?: string;
}

export const ComboboxOptions = ({
  children,
  className,
  variant,
}: ComboboxOptionsProps) => {
  const { isOpen, recreateOptionsFromChildren } = useComboboxContext();

  useEffect(() => {
    if (isOpen && children) {
      const childrenArray = Children.toArray(children);
      const optionValues = childrenArray
        .filter(
          (child) => isValidElement(child) && child.props?.value !== undefined,
        )
        .map((child) => (isValidElement(child) ? child.props.value : undefined))
        .filter((value) => value !== undefined);

      recreateOptionsFromChildren(optionValues);
    }
  }, [children, isOpen, recreateOptionsFromChildren]);

  if (!isOpen) return null;

  const hasNoChildren =
    !children || (Array.isArray(children) && children.length === 0);

  if (hasNoChildren) return null;

  return (
    <div
      className={mergeClasses(comboboxOptionsVariants({ variant, className }))}
    >
      {children}
    </div>
  );
};

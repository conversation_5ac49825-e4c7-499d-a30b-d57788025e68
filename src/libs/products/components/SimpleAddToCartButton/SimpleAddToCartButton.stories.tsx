import type { Meta, StoryObj } from '@storybook/react-vite';
import { SimpleAddToCartButton } from './SimpleAddToCartButton';
import { Flex } from '@/libs/ui/Flex/Flex';

const meta: Meta<typeof SimpleAddToCartButton> = {
  title: 'Product/SimpleAddToCartButton',
  component: SimpleAddToCartButton,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['sm', 'lg'],
    },
    isLoading: {
      control: { type: 'boolean' },
    },
    isDisabled: {
      control: { type: 'boolean' },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Large: Story = {
  args: {
    size: 'lg',
    isLoading: false,
    isDisabled: false,
  },
};

export const Small: Story = {
  args: {
    size: 'sm',
    isLoading: false,
    isDisabled: false,
  },
};

export const Loading: Story = {
  args: {
    size: 'lg',
    isLoading: true,
    isDisabled: false,
  },
};

export const Disabled: Story = {
  args: {
    size: 'lg',
    isLoading: false,
    isDisabled: true,
  },
};

export const Comparison: Story = {
  render: () => (
    <Flex gap="md" align="center">
      <div style={{ width: '200px' }}>
        <SimpleAddToCartButton size="lg" />
      </div>
      <div style={{ width: '60px' }}>
        <SimpleAddToCartButton size="sm" />
      </div>
    </Flex>
  ),
};

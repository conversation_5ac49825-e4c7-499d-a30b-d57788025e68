import { Text } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';
import { useTranslation } from 'react-i18next';
import { Icon } from '@/libs/icons/Icon';
import { mergeClasses } from '@/utils/tailwind';

export type SimpleAddToCartButtonProps = {
  isLoading?: boolean;
  isDisabled?: boolean;
  onClick?: () => void;
  size?: 'sm' | 'lg';
};

export const SimpleAddToCartButton = ({
  isLoading = false,
  isDisabled = false,
  size = 'lg',
  onClick,
}: SimpleAddToCartButtonProps) => {
  const { t } = useTranslation();

  const isSmall = size === 'sm';

  // Define min-width values for each variant
  const minWidthClass = isSmall ? 'min-w-[40px]' : 'min-w-[120px]';

  const buttonContent = isSmall ? (
    <div className="flex h-full items-center justify-center">
      <Icon name="cartSummary" aria-hidden="true" />
    </div>
  ) : (
    <Text className="text-center">{t('client.search.addToCart')}</Text>
  );

  return (
    <Button
      disabled={isDisabled}
      loading={isLoading}
      size={isSmall ? 'sm' : 'md'}
      onClick={onClick}
      className={mergeClasses(
        minWidthClass,
        'w-full',
        'bg-[var(--mantine-color-yellow-5)]',
        'text-[#222]',
        'hover:bg-[var(--mantine-color-yellow-6)]',
        'focus-visible:outline-2',
        'focus-visible:outline-[var(--mantine-color-yellow-6)]',
        'focus-visible:outline-offset-0.5',
      )}
      aria-label={isSmall ? t('client.search.addToCart') : undefined}
    >
      {buttonContent}
    </Button>
  );
};

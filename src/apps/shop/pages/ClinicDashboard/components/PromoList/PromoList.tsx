import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { PoweredBy } from '@/libs/ui/PoweredBy/PoweredBy';
import { PromoItem } from './PromoItem/PromoItem';
import { PromoType } from '@/types/common';
import { PromoDetailsModal } from './PromoDetailsModal/PromoDetailsModal';
import { AddedToCart } from './AddedToCart/AddedToCart';
import { useQuery } from '@tanstack/react-query';
import { EstimatedRebatesPanelLoader } from '../EstimatedRebatesPanel/EstimatedRebatesPanelLoader';
import { fetchApi } from '@/libs/utils/api';
import { queryKeys } from '@/libs/query/queryClient';

export const PromoList = () => {
  const loadPromosFunc = async () => {
    const response = await fetchApi<PromoType[]>(
      '/promotions?type=buy_x_get_y',
    );
    return response;
  };

  const { data: promos, isLoading } = useQuery({
    queryKey: queryKeys.promotions.buyXGetY(),
    queryFn: loadPromosFunc,
  });

  if (isLoading) return <EstimatedRebatesPanelLoader />;

  if (!promos || promos.length === 0) return null;

  return (
    <>
      <CollapsiblePanel
        header={
          <>
            <h3 className="py-[1.3rem] pr-4 pl-6 text-xl font-medium">
              Promo Matcher
            </h3>
            <PoweredBy className="relative mb-2 h-4 text-sm" />
          </>
        }
        content={
          <div className="flex flex-col">
            <div className="flex-col bg-white p-6">
              <h3 className="text-lg font-medium">Handpicked Deals for You!</h3>
              <p className="mb-8 text-sm text-black/80">
                We found the best deals for you - don&apos;t wait!
              </p>
              <div className="space-y-4 rounded-sm bg-black/2 p-4">
                {promos.map((promo) => (
                  <PromoItem key={promo.id} {...promo} />
                ))}
              </div>
            </div>
          </div>
        }
        startOpen
      />
      <PromoDetailsModal />
      <AddedToCart />
    </>
  );
};

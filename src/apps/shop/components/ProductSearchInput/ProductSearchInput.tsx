import { ChangeEvent, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';

import styles from './ProductSearchInput.module.css';

import { ProductType, SearchParamsProps } from '@/types';
import { useProductStore } from '@/apps/shop/stores/useProductStore/useProductStore';
import { buildQueryString } from '@/utils/buildQueryString';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';

import { useProductSuggestions } from './useProductSuggestions';
import { Combobox } from '@/libs/ui/Combobox/Combobox';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';
import { AddToCartInput } from '@/libs/products/components/AddToCartInput/AddToCartInput';
import { Group } from '@mantine/core';
import { AddToCartButton } from '@/libs/products/components/AddToCartForm/components/AddToCartButton/AddToCartButton';
import { StockStatusIcon } from '@/libs/products/components/StockStatusIcon/StockStatusIcon';

export const ProductSearchInput = () => {
  const { query, perPage, updateSearchQueryValue, getSearchProduct } =
    useProductStore();
  const { t } = useTranslation();
  const [selectedProduct, setSelectedProduct] = useState<string>('');

  const navigate = useNavigate();
  const { pathname } = useLocation();
  const [, setQueryParams] = useSearchParams();

  const { suggestions, previouslyOrderedItems, isSuggestionsLoading } =
    useProductSuggestions(query);

  useEffect(() => {
    setSelectedProduct('');
  }, [pathname]);

  const handleSearchQuery = async (value: string) => {
    const params = {
      query: value.trim(),
      page: 1,
      perPage,
      sortBy: undefined,
      sortOrder: undefined,
    };
    const newQuery = buildQueryString<SearchParamsProps<ProductType>>(params);

    if (pathname !== SHOP_ROUTES_PATH.search) {
      navigate(`${SHOP_ROUTES_PATH.search}?${newQuery}`);

      return;
    }
    getSearchProduct(params, setQueryParams);
  };

  const handleProductSelect = (value: unknown) => {
    const productName = value as string;
    if (productName) {
      setSelectedProduct(productName);
      updateSearchQueryValue(productName);
      handleSearchQuery(productName);
    }
  };

  const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    updateSearchQueryValue(value);
    setSelectedProduct('');
  };

  const handleEnterPress = (searchTerm: string) => {
    handleSearchQuery(searchTerm);
  };

  return (
    <Combobox
      value={selectedProduct || query}
      onChange={handleProductSelect}
      isLoading={isSuggestionsLoading}
    >
      <Combobox.Input
        placeholder={t('common.searchProducts')}
        onChange={handleInputChange}
        onEnterPress={handleEnterPress}
        className={styles.searchInput}
        displayValue={(value) => (value as string) || query}
      />

      {suggestions.length > 0 && (
        <Combobox.Options className="flex px-6 py-0">
          <div className="relative flex w-1/3 flex-col pt-3">
            <p className="mb-1 text-sm font-semibold">Results</p>
            <div className="overflow-y-scroll">
              {suggestions.slice(0, 7).map((suggestion) => (
                <Combobox.Option
                  key={suggestion}
                  value={suggestion}
                  className="pl-0"
                >
                  <Button variant="unstyled">
                    <Icon
                      name="magnifier"
                      size="1rem"
                      color="#B8B8B8"
                      className="mr-3"
                      aria-hidden={true}
                    ></Icon>
                    <span className="line-clamp-1 text-left">{suggestion}</span>
                  </Button>
                </Combobox.Option>
              ))}
            </div>
          </div>
          <div className="my-2 flex w-2/3 flex-col gap-2 border-l border-gray-200 pl-4">
            <p className="mb-1 text-sm font-semibold">Buy again</p>
            {previouslyOrderedItems.map((item) => (
              <div
                key={item.productOfferId}
                className="grid w-full grid-cols-[auto_1fr_auto] items-center rounded-[4px] border-[0.5px] border-gray-100 bg-gray-50 p-2 hover:border-[#0072C6] hover:bg-white"
              >
                <div className="mr-8">
                  <p className="mb-1 max-w-96 text-xs font-medium text-wrap text-black">
                    Carprovet (Carprofen) Caplet-Shaped Tablets for Dogs 75mg,
                    180 Count
                  </p>
                  <div className="text-sxs flex items-center gap-1">
                    <div className="[&_svg]:w-5">
                      <StockStatusIcon status={'IN_STOCK'} />
                    </div>
                    <div className="divider-v"></div>
                    <span className="text-sxs text-xs text-black">
                      {item.vendorName}
                    </span>
                    <div className="divider-v"></div>
                    <span>
                      <span>{item.quantity}</span>
                      <span>on</span>
                      <span>{item.lastOrderedAt}</span>
                    </span>
                  </div>
                </div>
                <div className="w-20 [&_svg]:w-3">
                  <AddToCartInput
                    originalAmount={item.quantity}
                    minIncrement={1}
                    onUpdate={() => {}}
                    size="sm"
                  />
                </div>
              </div>
            ))}
          </div>
        </Combobox.Options>
      )}
    </Combobox>
  );
};
